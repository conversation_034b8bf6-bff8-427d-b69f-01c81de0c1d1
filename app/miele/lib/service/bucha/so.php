<?php

class miele_service_bucha_so {

    /**
     * 生成物流费用补差订单
     * @param int $reshipWoId 退货工单ID
     * @param float $logisticsFee 物流费用
     * @param string $materialBn 物料编码
     * @return array 处理结果
     */
    public function generateLogisticsFeeOrder($reshipWoId, $logisticsFee, $materialBn= 'LOGISTICS_FEE_001') {
        try {
            // 获取退货工单信息
            $reshipModel = app::get('miele')->model('reship_wo');
            $reshipWo = $reshipModel->db_dump(['id' => $reshipWoId]);
            
            if (!$reshipWo) {
                return ['res' => 'fail', 'message' => '退货工单不存在'];
            }

            $orderType = 'ADJ-O';

            // 检查是否已生成物流费用补差订单
            $existingSo = $this->_checkExistingLogisticsSo($reshipWo['reship_bn'],$orderType);
            if ($existingSo) {
                return ['res' => 'fail', 'message' => '物流费用补差订单已存在'];
            }

            // 构建补差SO数据
            $soData = [
                'order_id' => $reshipWo['order_id'],
                'order_bn' => $reshipWo['order_bn'],
                'source' => 'reship',
                'platform_order_bn' => $reshipWo['reship_bn'],
                'order_type' => $orderType,
                'total_amount' => $logisticsFee,
                'sap_sync_status' => 'pending',
                'createtime' => time(),
                'paytime' => time(),
                'is_invoice' => '0',
                'order_status' => 'finish',
                'bill_label' => 'SOMS_ADJ_O',
                'shop_id' => $reshipWo['shop_id'],
                'shop_bn' => $reshipWo['shop_bn'],
                'shop_type' => $reshipWo['shop_type'],
                'at_time' => time(),
                'up_time' => time(),
            ];

            // 开始数据库事务
            $db = kernel::database();
            $db->beginTransaction();

            try {
                // 创建补差SO记录
                $sapSoModel = app::get('miele')->model('sap_so');
                $soId = $sapSoModel->save($soData);

                // 创建补差SO明细
                $this->_createBuchaSoItems($soId, $materialBn, $logisticsFee);

                // 记录操作日志
                $opObj = app::get('ome')->model('operation_log');
                $opObj->write_log('bucha_so@miele', $soId, "生成物流费用补差订单，金额：{$logisticsFee}元");

                $db->commit();

                return [
                    'res' => 'succ',
                    'message' => '物流费用补差订单生成成功',
                    'data' => [
                        'so_id' => $soId,
                        'amount' => $logisticsFee
                    ]
                ];

            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            kernel::log('物流费用补差订单生成失败: ' . $e->getMessage());
            return ['res' => 'fail', 'message' => '补差订单生成失败: ' . $e->getMessage()];
        }
    }

    /**
     * 生成手动补差订单
     * @param int $reshipWoId 退货工单ID
     * @param array $buchaItems 补差明细
     * @param string $buchaType 补差类型
     * @return array 处理结果
     */
    public function generateBuchaOrder($reshipWoId, $buchaItems, $buchaType = 'manual') {
        try {
            // 获取退货工单信息
            $reshipModel = app::get('miele')->model('reship_wo');
            $reshipWo = $reshipModel->db_dump(['id' => $reshipWoId]);
            
            if (!$reshipWo) {
                return ['res' => 'fail', 'message' => '退货工单不存在'];
            }

            // 计算总金额
            $totalAmount = 0;
            foreach ($buchaItems as $item) {
                $totalAmount += $item['bucha_amount'];
            }

            if ($totalAmount <= 0) {
                return ['res' => 'fail', 'message' => '补差金额必须大于0'];
            }

            // 构建补差SO数据
            $soData = [
                'order_id' => $reshipWo['order_id'],
                'order_bn' => $reshipWo['order_bn'],
                'source' => 'reship',
                'platform_order_bn' => $reshipWo['reship_bn'],
                'order_type' => 'BUCHA',
                'total_amount' => $totalAmount,
                'sap_sync_status' => 'pending',
                'createtime' => time(),
                'paytime' => time(),
                'is_invoice' => '0',
                'bill_label' => 'SOMS_ADJ_O',
                'order_status' => 'finish',
                'shop_id' => $reshipWo['shop_id'],
                'shop_bn' => $reshipWo['shop_bn'],
                'shop_type' => $reshipWo['shop_type'],
                'at_time' => time(),
                'up_time' => time(),
            ];

            // 开始数据库事务
            $db = kernel::database();
            $db->beginTransaction();

            try {
                // 创建补差SO记录
                $sapSoModel = app::get('miele')->model('sap_so');
                $soId = $sapSoModel->save($soData);

                // 创建补差SO明细
                foreach ($buchaItems as $item) {
                    $this->_createBuchaSoItems($soId, $item['bn'], $item['bucha_amount'], $item);
                }

                // 记录操作日志
                $opObj = app::get('ome')->model('operation_log');
                $opObj->write_log('bucha_so@miele', $soId, "生成手动补差订单，总金额：{$totalAmount}元");

                $db->commit();

                return [
                    'res' => 'succ',
                    'message' => '补差订单生成成功',
                    'data' => [
                        'so_id' => $soId,
                        'total_amount' => $totalAmount
                    ]
                ];

            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            kernel::log('手动补差订单生成失败: ' . $e->getMessage());
            return ['res' => 'fail', 'message' => '补差订单生成失败: ' . $e->getMessage()];
        }
    }

    /**
     * 创建补差SO明细
     * @param int $soId SO单ID
     * @param string $materialbn 物料编码
     * @param float $amount 金额
     * @param array $itemInfo 商品信息
     */
    private function _createBuchaSoItems($soId, $materialbn, $amount, $itemInfo = []) {
        $soItemModel = app::get('miele')->model('sap_so_items');

        $productInfo = $this->_getProductInfo($materialbn);

        $nums = $itemInfo['nums'] ? $itemInfo['nums'] : 1;
        $price = $itemInfo['price'] ? $itemInfo['price'] : bcdiv($amount, $nums, 2);

        $itemData = [
            'so_id' => $soId,
            'bn' => $materialbn,
            'product_id' => $productInfo['bm_id'] ? $productInfo['bm_id'] : 0,
            'name' => $productInfo['name'],
            'nums' => $nums,
            'price' => $price,
            'amount' => $amount,
            'sales_material_bn' => $itemInfo['sales_material_bn'] ?? $materialbn,
            'createtime' => time(),
            'divide_order_fee' => $amount,
            'actually_amount' => $amount,
            'settlement_amount' => $amount,
            'is_del' => 'false',
            'pay_status' => '1',
        ];
        
        $soItemModel->save($itemData);
    }

    /**
     * 检查是否已存在物流费用补差SO
     * @param int $reshipWoId 退货工单ID
     * @return array|null 已存在的SO记录
     */
    private function _checkExistingLogisticsSo($reshipBn,$orderType) {
        $sapSoModel = app::get('miele')->model('sap_so');
        return $sapSoModel->db_dump([
            'source' => 'reship',
            'platform_order_bn' => $reshipBn,
            'order_type' => $orderType
        ]);
    }

    private function _getProductInfo($materialbn) {
        $productModel = app::get('material')->model('base_material');
        $product = $productModel->db_dump(['material_bn' => $materialbn]);
        return $product;
    }
}
