<?php
/**
 * 补差单推送定时任务
 * 参考 app/miele/lib/autotask/timer/pushso.php 实现
 * <AUTHOR>
 * @version 1.0
 */
class miele_autotask_timer_pushbucha {
    
    /**
     * 任务锁key
     */
    const TASK_LOCK_KEY = 'miele_pushbucha_lock';
    
    /**
     * 锁超时时间(秒)
     */
    const LOCK_TIMEOUT = 300;

    /**
     * 任务执行入口
     * @param array $params 任务参数
     * @param string $err_msg 错误信息
     * @return bool 执行结果
     */
    public function process($params, &$err_msg) {
        try {
            // 检查任务锁
            if (!$this->acquireLock()) {
                $err_msg = '补差单推送任务正在执行中，请稍后再试';
                return true;
            }

            $soMdl = app::get('miele')->model('sap_so');
            
            // 获取待推送的补差单
            $buchaList = $soMdl->getList('id', [
                'sap_sync_status' => 'pending',
                'order_type' => 'BUCHA'
            ], 0, 100);
            
            if (!empty($buchaList)) {
                $syncDebitnote = kernel::single('miele_esb_syncdebitnote');
                $successCount = 0;
                
                foreach ($buchaList as $so) {
                    $result = $syncDebitnote->pushSo($so['id']);
                    if ($result['res'] == 'succ') {
                        $successCount++;
                    }
                }
                
                $err_msg = sprintf('补差单推送完成，总数：%d，成功：%d', 
                    count($buchaList), $successCount);
            } else {
                $err_msg = '没有需要推送的补差单';
            }

            // 完成释放锁
            $this->releaseLock();

            return true;
        } catch (Exception $e) {
            $err_msg = '补差单推送失败: ' . $e->getMessage();
            kernel::log('补差单推送任务异常: ' . $e->getMessage());
            return false;
        } finally {
            // 释放任务锁
            $this->releaseLock();
        }
    }
    
    /**
     * 获取任务锁
     * @return bool
     */
    private function acquireLock() {
        $kvstore = base_kvstore::instance('miele');
        
        // 检查锁是否存在
        $lockValue = null;
        if ($kvstore->fetch(self::TASK_LOCK_KEY, $lockValue)) {
            // 锁存在，检查是否过期
            if ($lockValue > (time() - self::LOCK_TIMEOUT)) {
                return false;
            }
        }
        
        // 设置锁
        return $kvstore->store(
            self::TASK_LOCK_KEY,
            time(),
            self::LOCK_TIMEOUT
        );
    }

    /**
     * 释放任务锁
     */
    private function releaseLock() {
        base_kvstore::instance('miele')->delete(self::TASK_LOCK_KEY);
    }
}
