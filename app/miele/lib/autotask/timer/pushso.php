<?php

/**
 * SO单推送定时任务
 * 将待同步的SO单推送到SAP系统
 * <AUTHOR>
 * @version 1.0
 */
class miele_autotask_timer_pushso 
{
    /**
     * 任务锁key
     */
    const TASK_LOCK_KEY = 'miele_pushso_lock';
    
    /**
     * 锁超时时间(秒)
     */
    const LOCK_TIMEOUT = 300;

    /**
     * 任务执行入口
     * @param array $params 任务参数
     * @param string $err_msg 错误信息
     * @return bool 执行结果
     */
    public function process($params, &$err_msg)
    {
        try {
            // 检查任务锁
            if (!$this->acquireLock()) {
                $err_msg = '任务正在执行中，请稍后再试';
                return true;
            }

            $soMdl = app::get('miele')->model('sap_so');
            
            // 处理普通SO单（排除BUCHA类型）
            $soList = $soMdl->getList('id', ['sap_sync_status' => 'pending', 'order_type|notin' => ['BUCHA','ADJ-O']], 0, 100);
            if (!empty($soList)) {
                $syncso = kernel::single('miele_esb_syncso');
                foreach ($soList as $so) {
                    $syncso->pushSo($so['id']);
                }
                $err_msg = sprintf('SO单推送完成，总数：%d', count($soList));
            } else {
                $err_msg = '没有需要同步的SO单';
            }

            // 完成释放锁
            $this->releaseLock();

            return true;
        } catch (Exception $e) {
            $err_msg = '推送失败: ' . $e->getMessage();
            kernel::log('SO单推送任务异常: ' . $e->getMessage());
            return false;
        } finally {
            // 释放任务锁
            $this->releaseLock();
        }
    }

    /**
     * 获取任务锁
     * @return bool
     */
    private function acquireLock() {
        $kvstore = base_kvstore::instance('miele');
        
        // 检查锁是否存在
        $lockValue = null;
        if ($kvstore->fetch(self::TASK_LOCK_KEY, $lockValue)) {
            // 锁存在，检查是否过期
            if ($lockValue > (time() - self::LOCK_TIMEOUT)) {
                return false;
            }
        }
        
        // 设置锁
        return $kvstore->store(
            self::TASK_LOCK_KEY,
            time(),
            self::LOCK_TIMEOUT
        );
    }

    /**
     * 释放任务锁
     */
    private function releaseLock() {
        base_kvstore::instance('miele')->delete(self::TASK_LOCK_KEY);
    }
}